<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>intact.devtools</groupId>
		<artifactId>intact-parent-pom-git</artifactId>
		<version>2.3.20</version>
	</parent>

	<groupId>intact.web.brokeroffice</groupId>
	<artifactId>broker-office-project</artifactId>
	<packaging>pom</packaging>

	<version>5.0.6-SNAPSHOT</version>

	<name>Intact Webzone App</name>
	<description>POM to build Webzone project (web, business)</description>
	<url>https://confluence.tooling.intactfc.cloud/pages/viewpage.action?pageId=645860431</url>

	<scm>
		<connection>scm:git:https://githubifc.iad.ca.inet/lab-se/webzone-app.git</connection>
		<developerConnection>scm:git:https://githubifc.iad.ca.inet/lab-se/webzone-app.git</developerConnection>
		<url>https://githubifc.iad.ca.inet/lab-se/webzone-app</url>
		<tag>HEAD</tag>
	</scm>

	<properties>
		<!-- Override parent properties -->
		<!-- Default configuration for TOOLCHAINS -->
		<jdk>17</jdk>
		<jdk.version>17</jdk.version>
		<!-- Used for toolchains. Sometimes can be different from jdk.version -->
		<java.version>${jdk.version}</java.version>
		<jdk.vendor>temurin</jdk.vendor>
		<mavenToolChainsPluginVersion>3.2.0</mavenToolChainsPluginVersion>

		<!-- Override parent config which is set to UTF-8 -->
		<project.build.sourceEncoding>ISO-8859-1</project.build.sourceEncoding>
		<project.build.artifactName>webzone</project.build.artifactName>

		<shortGroupId>web.brokeroffice</shortGroupId>
		<tools.version>2.2.0.7</tools.version>

		<plp.version>5.1.0</plp.version>
		<cif.version>4.0.9</cif.version>
		<brm.version>5.0.3</brm.version>

		<capi.version>2.4.0.39</capi.version>
		<abr.version>2.0.0.0</abr.version>
		<business-api.version>3.0.0.0</business-api.version>
		<plt.version>5.3.0.4-JAKARTA</plt.version>
		<security.version>1.60.0.23</security.version>
		<build_version>${project.version}</build_version>
		<oracle.ojdbc.version>12.1.0.1</oracle.ojdbc.version>
		<cxf.version>4.0.6</cxf.version>
		<jaxb-runtime.version>4.0.5</jaxb-runtime.version>
<!--		<jaxb-api.version>2.3.1</jaxb-api.version>-->
		<log4j.version>2.25.1</log4j.version>
		<selenium.version>2.53.1</selenium.version>
		<jetty.version>12.0.21</jetty.version>
		<slf4j.version>2.0.9</slf4j.version>
		<esapi.version>2.7.0.0</esapi.version>
		<slf4j.version>2.0.17</slf4j.version>

		<spring.version>6.0.23</spring.version>
		<hibernate.version>5.6.15.Final</hibernate.version>
		<jackson.version>2.19.2</jackson.version>
		<mavenJacocoPluginVersion>0.8.12</mavenJacocoPluginVersion>
		<mavenSurefirePluginVersion>3.5.3</mavenSurefirePluginVersion>
<!--		<mavenSurefirePluginVersion>2.22.2</mavenSurefirePluginVersion>-->
		<atomikos.version>6.0.0</atomikos.version>
	</properties>

	<profiles>
		<profile>
			<id>local</id>
			<modules>
				<module>broker-office-local-config</module>
			</modules>
			<dependencies>
				<!--
				When building with "local" profile, we add a special dependency to include local properties
				to the classpath. This ensures that we can deploy the project locally and to OCP without carrying
				extra config files in the WAR.
				-->
				<dependency>
					<groupId>intact.web.brokeroffice</groupId>
					<artifactId>broker-office-local-config</artifactId>
					<version>1.0.0</version>
				</dependency>
			</dependencies>
		</profile>
	</profiles>

	<modules>
		<module>broker-office-business</module>
		<module>broker-office-web</module>
	</modules>

	<dependencyManagement>
		<dependencies>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.14</version>
      </dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-web</artifactId>
				<version>6.2.9</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-vfs2</artifactId>
				<version>2.10.0</version>
			</dependency>
			<dependency>
				<groupId>net.minidev</groupId>
				<artifactId>json-smart</artifactId>
				<version>2.6.0</version>
			</dependency>
			<dependency>
				<groupId>org.jsoup</groupId>
				<artifactId>jsoup</artifactId>
				<version>1.21.1</version>
			</dependency>
			<dependency>
				<groupId>org.jdom</groupId>
				<artifactId>jdom2</artifactId>
				<version>2.0.6.1</version>
			</dependency>
			<dependency>
				<groupId>net.bytebuddy</groupId>
				<artifactId>byte-buddy</artifactId>
				<version>1.17.6</version>
			</dependency>
			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>2.14.0</version>
			</dependency>
			<dependency>
				<groupId>jakarta.inject</groupId>
				<artifactId>jakarta.inject-api</artifactId>
				<version>2.0.1.MR</version>
			</dependency>
			<dependency>
				<groupId>jakarta.activation</groupId>
				<artifactId>jakarta.activation-api</artifactId>
				<version>2.1.3</version>
			</dependency>
			<dependency>
				<groupId>jakarta.annotation</groupId>
				<artifactId>jakarta.annotation-api</artifactId>
				<version>1.3.5</version>
			</dependency>
			<dependency>
				<groupId>jakarta.persistence</groupId>
				<artifactId>jakarta.persistence-api</artifactId>
				<version>3.2.0</version>
			</dependency>
			<dependency>
				<groupId>jakarta.servlet</groupId>
				<artifactId>jakarta.servlet-api</artifactId>
				<version>6.1.0</version>
			</dependency>
			<dependency>
				<groupId>jakarta.xml.soap</groupId>
				<artifactId>jakarta.xml.soap-api</artifactId>
				<version>3.0.2</version>
			</dependency>
			<dependency>
				<groupId>jakarta.xml.bind</groupId>
				<artifactId>jakarta.xml.bind-api</artifactId>
				<version>4.0.2</version>
			</dependency>
			<dependency>
				<groupId>jakarta.xml.ws</groupId>
				<artifactId>jakarta.xml.ws-api</artifactId>
				<version>4.0.2</version>
			</dependency>
			<dependency>
				<!-- as a way to exclude the artifact, got a ClassCastException: -->
				<!-- org.hibernate.ejb.HibernatePersistence incompatible with -->
				<!-- javax.persistence.spi.PersistenceProvider -->
				<groupId>org.hibernate</groupId>
				<artifactId>ejb3-persistence</artifactId>
				<version>1.0.2.GA</version>
				<scope>provided</scope>
			</dependency>

			<dependency>
				<groupId>org.apache.xmlgraphics</groupId>
				<artifactId>batik-awt-util</artifactId>
				<version>1.19</version>
			</dependency>

			<dependency>
				<groupId>org.hibernate</groupId>
				<artifactId>hibernate-core-jakarta</artifactId>
				<version>${hibernate.version}</version>
			</dependency>

			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions-jta</artifactId>
				<version>${atomikos.version}</version>
				<classifier>jakarta</classifier>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions-jdbc</artifactId>
				<version>${atomikos.version}</version>
				<classifier>jakarta</classifier>
			</dependency>

			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.dataformat</groupId>
				<artifactId>jackson-dataformat-xml</artifactId>
				<version>${jackson.version}</version>
			</dependency>

			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-annotations</artifactId>
				<version>${jackson.version}</version>
			</dependency>

			<dependency>
				<groupId>org.codehaus.jettison</groupId>
				<artifactId>jettison</artifactId>
				<version>1.5.4</version>
			</dependency>

			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
				<version>${slf4j.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-simple</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>log4j-over-slf4j</artifactId>
				<version>${slf4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-java</artifactId>
				<version>${selenium.version}</version>
			</dependency>
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-api</artifactId>
				<version>${selenium.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>32.1.3-jre</version>
			</dependency>
			<dependency>
				<groupId>org.glassfish.jaxb</groupId>
				<artifactId>jaxb-runtime</artifactId>
				<version>${jaxb-runtime.version}</version>
			</dependency>

			<dependency>
				<groupId>org.eclipse.jetty</groupId>
				<artifactId>jetty-io</artifactId>
				<version>${jetty.version}</version>
			</dependency>

			<dependency>
				<groupId>org.eclipse.jetty</groupId>
				<artifactId>jetty-util</artifactId>
				<version>${jetty.version}</version>
			</dependency>

			<dependency>
				<groupId>org.eclipse.jetty</groupId>
				<artifactId>jetty-server</artifactId>
				<version>${jetty.version}</version>
			</dependency>

			<dependency>
				<groupId>org.eclipse.jetty</groupId>
				<artifactId>jetty-http</artifactId>
				<version>${jetty.version}</version>
			</dependency>

			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>1.11.0</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.20.0</version>
			</dependency>

			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>4.5.0</version>
			</dependency>

			<dependency>
				<groupId>org.codehaus.plexus</groupId>
				<artifactId>plexus-utils</artifactId>
				<version>3.6.0</version>
			</dependency>

			<!--esapi-->
			<dependency>
				<groupId>org.owasp.esapi</groupId>
				<artifactId>esapi</artifactId>
				<version>${esapi.version}</version>
			</dependency>

			<!--			SLF4J-->
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j.version}</version>
			</dependency>

			<!--			Spring BOM-->
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-framework-bom</artifactId>
				<scope>import</scope>
				<type>pom</type>
				<version>${spring.version}</version>
			</dependency>

			<dependency>
				<!-- External 3rdparty Dependencies -->
				<groupId>intact.devtools.intact-poms</groupId>
				<artifactId>intact-3rdparty-bom</artifactId>
				<type>pom</type>
				<scope>import</scope>
				<version>2.0.0.0</version>
				<exclusions>
					<exclusion>
						<groupId>commons-logging</groupId>
						<artifactId>commons-logging</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<!-- External Dependencies -->
				<groupId>intact.tools</groupId>
				<artifactId>tools-api-project</artifactId>
				<scope>import</scope>
				<version>${tools.version}</version>
				<type>pom</type>
			</dependency>

			<dependency>
				<!-- External Dependencies -->
				<groupId>intact.commons.utility</groupId>
				<artifactId>security-tools-project</artifactId>
				<scope>import</scope>
				<version>${security.version}</version>
				<type>pom</type>
				<exclusions>
					<exclusion>
						<!-- as it comes with JDK6 / WAS -->
						<groupId>xml-apis</groupId>
						<artifactId>xml-apis</artifactId>
					</exclusion>
					<exclusion>
						<groupId>xerces</groupId>
						<artifactId>xercesImpl</artifactId>
					</exclusion>
					<exclusion>
						<groupId>xalan</groupId>
						<artifactId>xalan</artifactId>
					</exclusion>
					<exclusion>
						<groupId>nl.captcha</groupId>
						<artifactId>simplecaptcha</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.jsoup</groupId>
						<artifactId>jsoup</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<!-- External Dependencies -->
				<groupId>intact.commons.service.cif</groupId>
				<artifactId>cif-project</artifactId>
				<scope>import</scope>
				<type>pom</type>
				<version>${cif.version}</version>
			</dependency>

			<dependency>
				<!-- External Dependencies -->
				<groupId>intact.commons.service.plp</groupId>
				<artifactId>plp-project</artifactId>
				<scope>import</scope>
				<type>pom</type>
				<version>${plp.version}</version>
			</dependency>

			<dependency>
				<groupId>intact.business</groupId>
				<artifactId>business-api-project</artifactId>
				<scope>import</scope>
				<version>${business-api.version}</version>
				<type>pom</type>
			</dependency>

			<dependency>
				<!-- External Dependencies -->
				<groupId>intact.commons.service.brm</groupId>
				<artifactId>brm-project</artifactId>
				<scope>import</scope>
				<type>pom</type>
				<version>${brm.version}</version>
			</dependency>

			<dependency>
				<!-- External Dependencies -->
				<groupId>intact.commons.capi</groupId>
				<artifactId>common-api-project</artifactId>
				<scope>import</scope>
				<type>pom</type>
				<version>${capi.version}</version>
			</dependency>

			<dependency>
				<!-- External Dependencies -->
				<groupId>intact.commons.plt</groupId>
				<artifactId>plt-service-client</artifactId>
				<version>${plt.version}</version>
			</dependency>

			<!-- =================== -->
			<!-- COMPONENT MODULES -->
			<!-- =================== -->
			<dependency>
				<groupId>intact.web.brokeroffice</groupId>
				<artifactId>broker-office-business</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>intact.web.brokeroffice</groupId>
				<artifactId>broker-office-web</artifactId>
				<type>war</type>
				<version>${project.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-javadoc-plugin</artifactId>
					<configuration>
						<skip>true</skip>
					</configuration>
				</plugin>
				<plugin>
					<artifactId>maven-compiler-plugin</artifactId>
					<configuration>
						<encoding>${project.build.sourceEncoding}</encoding>
						<parameters>true</parameters>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>${mavenSurefirePluginVersion}</version>
					<configuration>
						<includes>
							<include>**/*Test.java</include>
						</includes>
						<excludedGroups>junit-vintage</excludedGroups>
						<argLine>
							--add-opens java.base/java.lang=ALL-UNNAMED
							--add-opens java.base/java.time=ALL-UNNAMED
							--add-opens java.base/java.time.format=ALL-UNNAMED
							--add-opens java.base/java.util=ALL-UNNAMED
						</argLine>
					</configuration>
				</plugin>
				<plugin>
					<artifactId>maven-resources-plugin</artifactId>
					<configuration>
						<encoding>${project.build.sourceEncoding}</encoding>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<!-- Jacoco Maven Plugin -->
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-toolchains-plugin</artifactId>
				<version>${mavenToolChainsPluginVersion}</version>
				<executions>
					<execution>
						<goals>
							<goal>toolchain</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<toolchains>
						<jdk>
							<version>${jdk.version}</version>
							<vendor>${jdk.vendor}</vendor>
						</jdk>
					</toolchains>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
