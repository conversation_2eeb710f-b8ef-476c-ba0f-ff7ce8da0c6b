package com.intact.brokeroffice.controller.permission;

import com.ing.canada.cif.domain.IBrokersInfos;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.service.IBrokerService;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.common.builder.UserContextBuilder;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.profile.UserAccessProfile;
import com.intact.canada.brm.domain.user.UserAccount;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.faces.context.FacesContext;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.ing.canada.cif.domain.enums.LineOfBusinessEnum.COMMERCIAL_LINE;
import static com.ing.canada.cif.domain.enums.LineOfBusinessEnum.PERSONAL_LINE;

/**
 * The Class PermissionController.
 */
@Component
@Scope("session")
public class PermissionController {

	/**
	 * log
	 */
	private static final Log LOG = LogFactory.getLog(PermissionController.class);

	/**
	 * The authentification controller.
	 */
	@Autowired
	private AuthentificationController authentificationController;

	/**
	 * The accounts business process.
	 */
	@Autowired
	private IAccountsBusinessProcess accountsBusinessProcess;

	/**
	 * The broker service cif.
	 */
	@Autowired
	@Qualifier("cifBrokersService")
	private IBrokerService brokerService;

	@Inject
	private SystemDAO systemDAO;

	@Inject
	@Named("subBrokersServiceOPC")
	private ISubBrokersService subBrokerService = null;

	/**
	 * The command business process.
	 */
	@Autowired
	private ICommandBusinessProcess commandBusinessProcess;

	/**
	 * The ldap group admins.
	 */
	@Inject
	@Named("ldap-group-admins")
	private String ldapGroupAdmins;

	/**
	 * The ldap group program admins.
	 */
	@Inject
	@Named("ldap-group-program-admins")
	private String ldapGroupProgramAdmins;

	/**
	 * The ldap group quote admins.
	 */
	@Inject
	@Named("ldap-group-quote-admins")
	private String ldapGroupQuoteAdmins;

	/**
	 * The ldap group underwritter.
	 */
	@Inject
	@Named("ldap-group-underwritter")
	private String ldapGroupUnderwritter;

	/**
	 * The ldap group broker.
	 */
	@Inject
	@Named("ldap-group-broker")
	private String ldapGroupBroker;

	/**
	 * The ldap group broker reassign.
	 */
	@Inject
	@Named("ldap-group-broker-reassign")
	private String ldapGroupBrokerReassign;

	@Inject
	@Named("accesses.map")
	private Configuration accessesMap = null;

	@Inject
	private ProvinceController provinceController;

	@Inject
	private LanguageController languageController;

	public PermissionController() {

	}

	public final Configuration getAccessesMap() {
		return this.accessesMap;
	}

	public final void setAccessesMap(Configuration accessesMap) {
		this.accessesMap = accessesMap;
	}

	public IUserContext getUserContext() {
		IUserContext userContext = (IUserContext) FacesContext.getCurrentInstance()
																													.getExternalContext()
																													.getSessionMap()
																													.get("user.context");

		if (userContext == null) {
			userContext = UserContextBuilder.build(
					this.authentificationController,
					this,
					this.provinceController,
					this.languageController
			);

			FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("user.context", userContext);

		}

		return userContext;
	}

	/**
	 * Returns a string with the abbreviation of the users permission (this is
	 * mainly used to add details when adding notes/activities to a quote)
	 *
	 * @return the abbreviated group
	 */
	public String getAbbreviatedGroup() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (this.ldapGroupAdmins.equals(currentAccess) || this.ldapGroupProgramAdmins.equals(currentAccess)
				|| this.ldapGroupQuoteAdmins.equals(currentAccess)) {
			return " (INTACT)";
		}

		return "";
	}

	/**
	 * Check add followup notes. BR5390 Web Zone user access: BR5203 Do not display
	 * for Program Admin users.
	 *
	 * @return the check add followup notes
	 */
	public Boolean getCheckAddFollowupNotes() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupAdmins)
				|| currentAccess.startsWith(this.ldapGroupQuoteAdmins)
				|| currentAccess.startsWith(this.ldapGroupUnderwritter)
				|| currentAccess.startsWith(this.ldapGroupBroker)
				|| currentAccess.startsWith(this.ldapGroupBrokerReassign))) {

			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check upload. BR5203 Do not display for Program Admin users. BR5390 Web Zone
	 * user access:
	 *
	 * @return the check upload
	 */
	public Boolean getCheckUpload() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupAdmins)
				|| currentAccess.startsWith(this.ldapGroupQuoteAdmins)
				|| currentAccess.startsWith(this.ldapGroupUnderwritter)
				|| currentAccess.startsWith(this.ldapGroupBroker))) {

			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check reassign.
	 * <p>
	 * BR5103 The check box will only be displayed if the user has an Intact Super
	 * Admin or Quote Admin account. BR5390 Web Zone user access:
	 *
	 * @return the check reassign
	 */
	public Boolean getCheckReassign() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupAdmins)
				|| currentAccess.startsWith(this.ldapGroupQuoteAdmins)
				|| currentAccess.startsWith(this.ldapGroupBrokerReassign))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check account management. BR5390 Web Zone user access:
	 *
	 * @return the check account management
	 */
	public Boolean getCheckAccountManagement() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins)
				|| currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Current access
	 *
	 * @return
	 */
	public String getAccess() {
		return this.authentificationController.getCurrentAccessLevel();
	}

	public boolean getCheckSearchAccess() {

		boolean access = Boolean.parseBoolean(
				(String) this.getAccessesMap().getValue("search.extended." + this.provinceController.getCompany()));

		return (this.getAccess() != null) && (getAccess().startsWith(this.ldapGroupAdmins)
				|| getAccess().startsWith(this.ldapGroupBrokerReassign) || access);
	}

	/**
	 * Check manage account access. BR5390 Web Zone user access:
	 *
	 * @return the check manage account access
	 */
	public Boolean getCheckManageAccountAccess() {
		return (this.authentificationController.getCurrentAccessLevel() != null
				&& this.authentificationController.getCurrentAccessLevel().startsWith(this.ldapGroupAdmins));
	}

	/**
	 * Check manage sub broker profile. BR5390 Web Zone user access:
	 *
	 * @return the check manage sub broker profile
	 */
	public Boolean getCheckManageSubBrokerProfile() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins)
				|| currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check manage fsa. BR5390 Web Zone user access:
	 *
	 * @return the check manage fsa
	 */
	public Boolean getCheckManageFsa() {

		if (this.authentificationController.getCurrentAccessLevel() != null
				&& this.authentificationController.getCurrentAccessLevel().startsWith(this.ldapGroupAdmins)) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check reports. BR5390 Web Zone user access:
	 *
	 * @return the check reports
	 */
	public Boolean getCheckReports() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();
		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins)
				|| currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check view change report. BR5390 Web Zone user access:
	 *
	 * @return the check view change report
	 */
	public Boolean getCheckViewChangeReport() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();
		/*
		 * if(this.ldapGroupProgramAdmins.equals(currentAccess) ||
		 * this.ldapGroupAdmins.equals(currentAccess)){
		 */
		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins)
				|| currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check view kpi report. BR5390 Web Zone user access:
	 *
	 * @return the check view kpi report
	 */
	public Boolean getCheckViewKpiReport() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();
		/*
		 * if(this.ldapGroupProgramAdmins.equals(currentAccess) ||
		 * this.ldapGroupAdmins.equals(currentAccess)){
		 */
		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins)
				|| currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * @return true if the user has access to personal line of business
	 */
	public boolean isPersonalLinesAccessProfile() {
		return getAvailableLinesOfBusiness().contains(PERSONAL_LINE);
	}

	/**
	 * @return true if the user has access to commercial line of business
	 */
	public boolean isIRCACommercialQQAccessProfile() {
		return getAvailableLinesOfBusiness().contains(COMMERCIAL_LINE);
	}

	/**
	 * @return all lines of business available to the current account in session
	 */
	public Set<LineOfBusinessEnum> getAvailableLinesOfBusiness() {
		try {
			return getAllAccessibleLinesOfBusiness();
		} catch (BrokerServiceException e) {
			LOG.error(e.getMessage());
		}

		return Collections.emptySet();
	}

	/**
	 * Obtain all accessible lines of business based on the current account.
	 * <p>
	 * Only {@link LineOfBusinessCodeEnum#COMMERCIAL_LINES} are available on Webzone now.
	 * {@link LineOfBusinessCodeEnum#PERSONAL_LINES} are in Contact PL.
	 */
	private Set<LineOfBusinessEnum> getAllAccessibleLinesOfBusiness() throws BrokerServiceException {
		Set<LineOfBusinessEnum> commercialLineSet = new HashSet<>(1);

		if (this.authentificationController.isMasterRole()) {
			commercialLineSet.add(COMMERCIAL_LINE);
			return commercialLineSet;
		}

		String userName = this.authentificationController.getCurrentAccountUId();
		if (userName == null) {
			return commercialLineSet;
		}

		UserAccount userAccount = this.accountsBusinessProcess.findByUId(userName);
		if (userAccount == null || userAccount.getUserAccessProfiles() == null) {
			return commercialLineSet;
		}

		for (UserAccessProfile userAccessProfile : userAccount.getUserAccessProfiles()) {
			if (userAccessProfile.getExpiryDate() == null) {
				if (AccessProfileEnum.IRCA == userAccessProfile.getAccessProfile().getName()) {
					commercialLineSet.add(COMMERCIAL_LINE);
				}
			}
		}

		return commercialLineSet;
	}

	/**
	 * Gets the check quote access.
	 * <p>
	 * BR5113 If the current user is a participating point of sale and the selected
	 * quote has been reassigned to another point of sale since the List of
	 * Quotes/Search Results were first displayed, a message indicating the selected
	 * quote is no longer available w BR5390 Web Zone user access:
	 *
	 * @return the check quote access
	 * @throws BrokerServiceException
	 */

	public Boolean getCheckQuoteAccess() throws BrokerServiceException {

		Boolean checkQuoteAccess = false;

		ApplicationIdEnum app = null;
		LineOfBusinessEnum lob = null;
		String company = null;
		this.getCurrentContext(company, app, lob);

		if (this.authentificationController.isMasterRole()) {
			checkQuoteAccess = true;
		} else {

			checkQuoteAccess = false;

			String userName = this.authentificationController.getCurrentAccountUId();

			if (userName != null) {
				UserAccount userAccount = this.accountsBusinessProcess.findByUId(userName);

				if (userAccount != null) {
					String masterCode = getMasterCode();
					if (masterCode != null) {
						for (BrokerWebOfficeAccess brokerWebOfficeAccess : userAccount.getBrokerWebOfficeAccesses()) {
							if (brokerWebOfficeAccess.getMasterOwnerCode().equals(masterCode)) {
								checkQuoteAccess = Boolean.TRUE;
							}
						}
					}
				}
			}
		}

		if (!checkQuoteAccess && this.isAllowed(company, app, lob)) {
			checkQuoteAccess = true;
		}

		return checkQuoteAccess;
	}

	protected boolean isAllowed(String company, ApplicationIdEnum app, LineOfBusinessEnum lob) {

		List<String> brokers = this.authentificationController.getAvailableMasters();
		return this.brokerService.checkAccess(brokers, company, app, lob);
	}

	protected void getCurrentContext(String company, ApplicationIdEnum app, LineOfBusinessEnum lob) {

		ApplicationModeEnum mode = null;

		// get Reference number
		String aReferenceNo = (String) FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
				.get("referenceNo");
		if (aReferenceNo == null) {
			// Requests comming from the QuoteView
			aReferenceNo = (String) FacesContext.getCurrentInstance().getViewRoot().getAttributes().get("referenceNo");
		}

		PolicyVersion policy = this.commandBusinessProcess.findLatestPolicyVersion(aReferenceNo);

		if (policy != null) {
			mode = policy.getInsurancePolicy().getApplicationMode();
			if (mode != null) {
				if (mode == ApplicationModeEnum.REGULAR_QUOTE) {
					app = ApplicationIdEnum.AUTO_REGULAR_QUOTE;
				} else if (mode == ApplicationModeEnum.QUICK_QUOTE) {
					app = ApplicationIdEnum.AUTO_QUICKQUOTE;
				}
			}
			lob = LineOfBusinessEnum.valueOfCode(policy.getInsurancePolicy().getLineOfBusiness().getCode());
			company = policy.getInsurancePolicy().getManufacturerCompany().getCode();
		} else {
			app = ApplicationIdEnum.AUTO_QUICKQUOTE;
			lob = LineOfBusinessEnum.PERSONAL_LINE;
		}
	}

	/**
	 * Gets the master code.
	 *
	 * @return the master code
	 */
	private String getMasterCode() {
		// QuoteView requested with parameters
		String referenceNo = (String) FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
				.get("referenceNo");

		if (referenceNo == null) {
			// Requests coming from the QuoteView
			referenceNo = (String) FacesContext.getCurrentInstance().getViewRoot().getAttributes().get("referenceNo");
		}
		PolicyVersion policyVersion = this.commandBusinessProcess.findLatestPolicyVersion(referenceNo);

		if (policyVersion == null) {
			return null;
		}

		IBrokersInfos brokersInfos = this.brokerService.getBrokersInfosBySubBrokerId(
				policyVersion.getInsurancePolicy().getLatestSubBrokerAssignment().getCifSubBrokerId(),
				this.provinceController.getCompanyEnumCode().getMasterBrokerCompanyNumber());

		return brokersInfos != null ? brokersInfos.getMasterOwnerNo() : null;
	}

	/**
	 * Check assign. Updload to contact is automatic. Assign replaces upload.
	 *
	 * @return the check assign
	 */
	public Boolean checkAssign(String quoteBrokerNumber) {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null
				&& currentAccess.startsWith(this.ldapGroupBroker)
				&& !this.isSameBrokerNumber(quoteBrokerNumber)) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	private boolean isSameBrokerNumber(String quoteBrokerNumber) {
		String master = this.systemDAO.getMasterBroker();
		if (master != null) {
			ISubBrokers broker = this.subBrokerService.getSubBroker(master, this.systemDAO.getCompany());
			if (quoteBrokerNumber != null && broker != null && broker.getSubBrokerNumber() != null) {
				return broker.getSubBrokerNumber().equals(quoteBrokerNumber);
			}
		}
		return false;
	}

}
